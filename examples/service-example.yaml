# ========== RayCluster 服务示例 ==========
# RayCluster 服务：只有 managed-by 注解，没有 online-dev-type 注解
# 使用 delegate 模式，网关路由前缀使用 ray.io/cluster 标签值
apiVersion: v1
kind: Service
metadata:
  name: ray-head-service
  namespace: infra
  annotations:
    managed-by: kuberay-dynamic-route
    # 注意：没有 online-dev-type 注解
  labels:
    ray.io/cluster: mlops-cluster  # 网关路由前缀：/mlops-cluster/
spec:
  selector:
    app: ray-head
  ports:
  - name: dashboard
    port: 8265
    targetPort: 8265
    protocol: TCP
  - name: client
    port: 10001
    targetPort: 10001
    protocol: TCP

---
# ========== OnlineDev 服务示例 ==========
# OnlineDev code-server 服务：有 managed-by 和 online-dev-type 注解
# 使用直接路由，网关路由前缀使用服务名，包含 rewrite 规则
apiVersion: v1
kind: Service
metadata:
  name: mlops-code-server
  namespace: infra
  annotations:
    managed-by: kuberay-dynamic-route
    online-dev-type: code-server  # 会添加 rewrite 规则
spec:
  selector:
    app: mlops-code-server
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP

---
# OnlineDev jupyter 服务：有 managed-by 和 online-dev-type 注解
# 使用直接路由，网关路由前缀使用服务名，不包含 rewrite 规则
apiVersion: v1
kind: Service
metadata:
  name: mlops-jupyter
  namespace: infra
  annotations:
    managed-by: kuberay-dynamic-route
    online-dev-type: jupyter  # 不会添加 rewrite 规则
spec:
  selector:
    app: mlops-jupyter
  ports:
  - name: http
    port: 8888
    targetPort: 8888
    protocol: TCP
