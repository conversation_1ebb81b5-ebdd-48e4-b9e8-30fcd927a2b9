# 示例：code-server 类型的服务
# 生成的网关路由将使用 /mlops-cluster/ 作为前缀，并包含 rewrite 规则
apiVersion: v1
kind: Service
metadata:
  name: mlops-code-server
  namespace: infra
  annotations:
    managed-by: kuberay-dynamic-route
    online-dev-type: code-server
  labels:
    ray.io/cluster: mlops-cluster  # 这个值将用作网关路由的前缀
spec:
  selector:
    app: mlops-code-server
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP

---
# 示例：jupyter 类型的服务
# 生成的网关路由将使用 /mlops-cluster/ 作为前缀，但不包含 rewrite 规则
apiVersion: v1
kind: Service
metadata:
  name: mlops-jupyter
  namespace: infra
  annotations:
    managed-by: kuberay-dynamic-route
    online-dev-type: jupyter
  labels:
    ray.io/cluster: mlops-cluster  # 这个值将用作网关路由的前缀
spec:
  selector:
    app: mlops-jupyter
  ports:
  - name: http
    port: 8888
    targetPort: 8888
    protocol: TCP
