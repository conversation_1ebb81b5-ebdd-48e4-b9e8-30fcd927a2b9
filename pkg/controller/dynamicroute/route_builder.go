package dynamicroute

import (
	"fmt"
	"istio.io/api/networking/v1beta1"
)

// RouteBuilder 负责构建各种路由规则
type RouteBuilder struct{}

// NewRouteBuilder 创建新的路由构建器
func NewRouteBuilder() *RouteBuilder {
	return &RouteBuilder{}
}

// BuildRayClusterGatewayRoute 为 RayCluster 服务创建网关路由规则（使用 delegate 模式）
func (rb *RouteBuilder) BuildRayClusterGatewayRoute(serviceInfo *ServiceInfo) *v1beta1.HTTPRoute {
	return &v1beta1.HTTPRoute{
		Match: []*v1beta1.HTTPMatchRequest{
			{
				Uri: &v1beta1.StringMatch{
					MatchType: &v1beta1.StringMatch_Prefix{
						Prefix: fmt.Sprintf("/%s/", serviceInfo.RayClusterName),
					},
				},
			},
		},
		Delegate: &v1beta1.Delegate{
			Name:      fmt.Sprintf(ServiceVSNameFormat, serviceInfo.Name),
			Namespace: VirtualServiceNamespace,
		},
		Rewrite: &v1beta1.HTTPRewrite{
			Uri: "/",
		},
	}
}

// BuildOnlineDevGatewayRoute 为 OnlineDev 服务创建网关路由规则（直接路由）
func (rb *RouteBuilder) BuildOnlineDevGatewayRoute(serviceInfo *ServiceInfo, port ServicePort) *v1beta1.HTTPRoute {
	route := &v1beta1.HTTPRoute{
		Match: []*v1beta1.HTTPMatchRequest{
			{
				Port: uint32(port.Port),
				Uri: &v1beta1.StringMatch{
					MatchType: &v1beta1.StringMatch_Prefix{
						Prefix: fmt.Sprintf("/%s/", serviceInfo.Name),
					},
				},
			},
		},
		Name: serviceInfo.Name,
		Route: []*v1beta1.HTTPRouteDestination{
			{
				Destination: &v1beta1.Destination{
					Host: fmt.Sprintf(ServiceFqdnFormat, serviceInfo.Name, serviceInfo.Namespace),
					Port: &v1beta1.PortSelector{
						Number: uint32(port.Port),
					},
				},
			},
		},
	}

	// 根据类型决定是否添加 rewrite
	if shouldIncludeRewrite(serviceInfo.DevType) {
		route.Rewrite = &v1beta1.HTTPRewrite{
			Uri: "/",
		}
	}

	return route
}

// BuildServiceRoute 为服务级别的 VirtualService 构建路由规则
func (rb *RouteBuilder) BuildServiceRoute(serviceInfo *ServiceInfo, port ServicePort) *v1beta1.HTTPRoute {
	return &v1beta1.HTTPRoute{
		Match: []*v1beta1.HTTPMatchRequest{
			{
				Port: uint32(port.Port),
			},
		},
		Route: []*v1beta1.HTTPRouteDestination{
			{
				Destination: &v1beta1.Destination{
					Host: fmt.Sprintf(ServiceFqdnFormat, serviceInfo.Name, serviceInfo.Namespace),
					Port: &v1beta1.PortSelector{
						Number: uint32(port.Port),
					},
				},
			},
		},
	}
}

// BuildDefaultRoute 构建默认路由规则
func (rb *RouteBuilder) BuildDefaultRoute(serviceInfo *ServiceInfo, defaultPort ServicePort) *v1beta1.HTTPRoute {
	return &v1beta1.HTTPRoute{
		Route: []*v1beta1.HTTPRouteDestination{
			{
				Destination: &v1beta1.Destination{
					Host: fmt.Sprintf(ServiceFqdnFormat, serviceInfo.Name, serviceInfo.Namespace),
					Port: &v1beta1.PortSelector{
						Number: uint32(defaultPort.Port),
					},
				},
			},
		},
	}
}

// BuildGatewayRoutes 根据服务类型构建网关路由规则
func (rb *RouteBuilder) BuildGatewayRoutes(serviceInfo *ServiceInfo) []*v1beta1.HTTPRoute {
	if isOnlineDevService := serviceInfo.DevType != ""; isOnlineDevService {
		// OnlineDev 服务：为每个端口创建直接路由
		routes := make([]*v1beta1.HTTPRoute, 0, len(serviceInfo.Ports))
		for _, port := range serviceInfo.Ports {
			route := rb.BuildOnlineDevGatewayRoute(serviceInfo, port)
			routes = append(routes, route)
		}
		return routes
	} else {
		// RayCluster 服务：创建单个 delegate 路由
		route := rb.BuildRayClusterGatewayRoute(serviceInfo)
		return []*v1beta1.HTTPRoute{route}
	}
}

// BuildServiceRoutes 为服务级别的 VirtualService 构建所有路由规则
func (rb *RouteBuilder) BuildServiceRoutes(serviceInfo *ServiceInfo) []*v1beta1.HTTPRoute {
	routes := make([]*v1beta1.HTTPRoute, 0, len(serviceInfo.Ports)+1)

	// 为每个端口创建路由规则
	for _, port := range serviceInfo.Ports {
		route := rb.BuildServiceRoute(serviceInfo, port)
		routes = append(routes, route)
	}

	// 添加默认路由
	if len(serviceInfo.Ports) > 0 {
		defaultRoute := rb.BuildDefaultRoute(serviceInfo, serviceInfo.Ports[0])
		routes = append(routes, defaultRoute)
	}

	return routes
}
