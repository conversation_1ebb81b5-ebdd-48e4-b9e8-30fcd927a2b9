package dynamicroute

import (
	"context"
	"fmt"
	corev1types "k8s.io/api/core/v1"
	corev1 "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"
)

// ServiceHandler 负责处理服务相关的业务逻辑
type ServiceHandler struct {
	svcLister corev1.ServiceLister
	vsManager *VirtualServiceManager
}

// NewServiceHandler 创建新的服务处理器
func NewServiceHandler(svcLister corev1.ServiceLister, vsManager *VirtualServiceManager) *ServiceHandler {
	return &ServiceHandler{
		svcLister: svcLister,
		vsManager: vsManager,
	}
}

// HandleAddService 处理服务添加事件
func (sh *ServiceHandler) HandleAddService(ctx context.Context, key string) error {
	ns, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		klog.Errorf("解析服务键 %s 失败: %v", key, err)
		return err
	}

	klog.Infof("处理服务添加事件: %s/%s", ns, name)

	svc, err := sh.svcLister.Services(ns).Get(name)
	if err != nil {
		klog.Errorf("获取服务 %s/%s 失败: %v", ns, name, err)
		return err
	}

	// 检查是否为管理的服务
	if !sh.isManagedService(svc) {
		klog.Infof("忽略未标记的服务: %s/%s", svc.Namespace, svc.Name)
		return nil
	}

	// 提取服务信息
	serviceInfo, err := sh.extractAndValidateServiceInfo(svc)
	if err != nil {
		klog.Errorf("提取服务 %s/%s 信息失败: %v", svc.Namespace, svc.Name, err)
		return err
	}

	klog.Infof("开始处理标记为 %s=%s 的服务: %s/%s，类型: %s",
		ManagedByAnnotationKey, ManagedByAnnotationValue,
		serviceInfo.Namespace, serviceInfo.Name, serviceInfo.DevType)

	// 处理 VirtualService
	return sh.processServiceVirtualServices(ctx, serviceInfo)
}

// HandleDeleteService 处理服务删除事件
func (sh *ServiceHandler) HandleDeleteService(ctx context.Context, key string) error {
	ns, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		klog.Errorf("解析服务键 %s 失败: %v", key, err)
		return err
	}

	klog.Infof("处理服务删除事件: %s/%s", ns, name)

	// 对于删除事件，我们需要尝试获取服务信息来判断服务类型
	// 但是服务可能已经被删除，所以我们需要从缓存中获取
	svc, err := sh.svcLister.Services(ns).Get(name)

	routeKey := name // 默认使用服务名作为路由键
	isOnlineDev := false

	if err == nil {
		// 能获取到服务，判断类型
		if isOnlineDevService(svc) {
			isOnlineDev = true
			routeKey = name // OnlineDev 服务使用服务名
		} else if svc.Labels != nil {
			// RayCluster 服务使用 rayClusterName
			if clusterName, exists := svc.Labels[RayClusterLabelKey]; exists {
				routeKey = clusterName
			}
		}
	}

	return sh.vsManager.RemoveServiceFromVS(ctx, name, routeKey, isOnlineDev)
}

// isManagedService 检查服务是否为管理的服务
func (sh *ServiceHandler) isManagedService(svc *corev1types.Service) bool {
	annotations := svc.GetAnnotations()
	if annotations == nil {
		return false
	}

	value, exists := annotations[ManagedByAnnotationKey]
	return exists && value == ManagedByAnnotationValue
}

// extractAndValidateServiceInfo 提取并验证服务信息
func (sh *ServiceHandler) extractAndValidateServiceInfo(svc *corev1types.Service) (*ServiceInfo, error) {
	// 检查服务是否有端口
	if len(svc.Spec.Ports) == 0 {
		return nil, fmt.Errorf("service %s/%s has no ports defined", svc.Namespace, svc.Name)
	}

	// 提取服务信息
	serviceInfo, err := extractServiceInfo(svc)
	if err != nil {
		return nil, err
	}

	// 记录服务的所有端口
	portInfo := formatPortInfo(serviceInfo.Ports)
	klog.Infof("服务 %s/%s 包含的端口: %s", serviceInfo.Namespace, serviceInfo.Name, portInfo)

	return serviceInfo, nil
}

// processServiceVirtualServices 处理服务的 VirtualService 创建和更新
func (sh *ServiceHandler) processServiceVirtualServices(ctx context.Context, serviceInfo *ServiceInfo) error {
	// 1. 创建或更新服务级别的 VS
	if err := sh.vsManager.CreateOrUpdateServiceVS(ctx, serviceInfo); err != nil {
		return fmt.Errorf("处理服务级 VirtualService 失败: %v", err)
	}

	// 2. 创建或更新网关级别的 VS
	if err := sh.vsManager.CreateOrUpdateGatewayVS(ctx, serviceInfo); err != nil {
		return fmt.Errorf("处理网关级 VirtualService 失败: %v", err)
	}

	klog.Infof("成功处理服务 %s/%s 的 VirtualService", serviceInfo.Namespace, serviceInfo.Name)
	return nil
}
