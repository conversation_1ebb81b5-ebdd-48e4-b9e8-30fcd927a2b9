package dynamicroute

import (
	"context"
	"fmt"
	"istio.io/api/networking/v1beta1"
	istiov1beta1 "istio.io/client-go/pkg/apis/networking/v1beta1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"
	"kuberay-dynamic-route-manager/pkg/client"
)

// VirtualServiceManager 负责管理 VirtualService 资源
type VirtualServiceManager struct {
	istioClient  client.IstioClientInterface
	routeBuilder *RouteBuilder
}

// NewVirtualServiceManager 创建新的 VirtualService 管理器
func NewVirtualServiceManager(istioClient client.IstioClientInterface) *VirtualServiceManager {
	return &VirtualServiceManager{
		istioClient:  istioClient,
		routeBuilder: NewRouteBuilder(),
	}
}

// CreateOrUpdateServiceVS 创建或更新服务级别的 VirtualService（只有 RayCluster 服务需要）
func (vsm *VirtualServiceManager) CreateOrUpdateServiceVS(ctx context.Context, serviceInfo *ServiceInfo) error {
	// OnlineDev 服务不需要服务级别的 VirtualService
	if serviceInfo.DevType != "" {
		klog.Infof("OnlineDev 服务 %s/%s 不需要服务级 VirtualService", serviceInfo.Namespace, serviceInfo.Name)
		return nil
	}

	serviceVSName := fmt.Sprintf(ServiceVSNameFormat, serviceInfo.Name)
	klog.Infof("RayCluster 服务 %s/%s 对应的 VirtualService 名称: %s", serviceInfo.Namespace, serviceInfo.Name, serviceVSName)

	// 检查是否已存在
	_, err := vsm.istioClient.GetVirtualService(ctx, VirtualServiceNamespace, serviceVSName, metav1.GetOptions{})
	if err != nil {
		if !k8sErrors.IsNotFound(err) {
			klog.Errorf("获取 VirtualService %s/%s 失败: %v", VirtualServiceNamespace, serviceVSName, err)
			return fmt.Errorf("failed to get service VirtualService: %v", err)
		}

		// 创建新的服务级别 VS
		return vsm.createServiceVS(ctx, serviceInfo, serviceVSName)
	}

	klog.Infof("服务级 VirtualService %s/%s 已存在，跳过创建", VirtualServiceNamespace, serviceVSName)
	return nil
}

// createServiceVS 创建服务级别的 VirtualService
func (vsm *VirtualServiceManager) createServiceVS(ctx context.Context, serviceInfo *ServiceInfo, serviceVSName string) error {
	klog.Infof("开始创建新的服务级 VirtualService: %s/%s", VirtualServiceNamespace, serviceVSName)

	// 构建路由规则
	httpRoutes := vsm.routeBuilder.BuildServiceRoutes(serviceInfo)

	newServiceVS := &istiov1beta1.VirtualService{
		ObjectMeta: metav1.ObjectMeta{
			Name:      serviceVSName,
			Namespace: VirtualServiceNamespace,
			Annotations: map[string]string{
				ManagedByAnnotationKey: ManagedByAnnotationValue,
			},
		},
		Spec: v1beta1.VirtualService{
			Http: httpRoutes,
		},
	}

	if err := vsm.istioClient.CreateVirtualService(ctx, VirtualServiceNamespace, newServiceVS, metav1.CreateOptions{}); err != nil {
		return fmt.Errorf("failed to create service VirtualService: %v", err)
	}

	klog.Infof("成功创建服务级 VirtualService: %s/%s", VirtualServiceNamespace, serviceVSName)
	return nil
}

// CreateOrUpdateGatewayVS 创建或更新网关级别的 VirtualService
func (vsm *VirtualServiceManager) CreateOrUpdateGatewayVS(ctx context.Context, serviceInfo *ServiceInfo) error {
	klog.Infof("开始处理网关级 VirtualService: %s/%s", VirtualServiceNamespace, GatewayVirtualServiceName)

	gatewayVS, err := vsm.istioClient.GetVirtualService(ctx, VirtualServiceNamespace, GatewayVirtualServiceName, metav1.GetOptions{})
	if err != nil {
		if !k8sErrors.IsNotFound(err) {
			klog.Errorf("获取网关级 VirtualService %s/%s 失败: %v", VirtualServiceNamespace, GatewayVirtualServiceName, err)
			return fmt.Errorf("failed to get gateway VirtualService: %v", err)
		}

		// 创建新的网关级别 VS
		return vsm.createGatewayVS(ctx, serviceInfo)
	}

	// 更新现有的网关级别 VS
	return vsm.updateGatewayVS(ctx, gatewayVS, serviceInfo)
}

// createGatewayVS 创建网关级别的 VirtualService
func (vsm *VirtualServiceManager) createGatewayVS(ctx context.Context, serviceInfo *ServiceInfo) error {
	klog.Infof("网关级 VirtualService %s/%s 不存在，开始创建", VirtualServiceNamespace, GatewayVirtualServiceName)

	// 构建网关路由规则
	gatewayRoutes := vsm.routeBuilder.BuildGatewayRoutes(serviceInfo)

	newGatewayVS := &istiov1beta1.VirtualService{
		ObjectMeta: metav1.ObjectMeta{
			Name:      GatewayVirtualServiceName,
			Namespace: VirtualServiceNamespace,
			Annotations: map[string]string{
				ManagedByAnnotationKey: ManagedByAnnotationValue,
			},
		},
		Spec: v1beta1.VirtualService{
			Hosts:    []string{DefaultHosts},
			Gateways: []string{DefaultGateway},
			Http:     gatewayRoutes,
		},
	}

	klog.Infof("创建网关级 VirtualService %s/%s，包含服务 %s (cluster: %s) 的路由规则",
		VirtualServiceNamespace, GatewayVirtualServiceName, serviceInfo.Name, serviceInfo.RayClusterName)
	if err := vsm.istioClient.CreateVirtualService(ctx, VirtualServiceNamespace, newGatewayVS, metav1.CreateOptions{}); err != nil {
		klog.Errorf("创建网关级 VirtualService 失败: %v", err)
		return fmt.Errorf("failed to create gateway VirtualService: %v", err)
	}
	klog.Infof("成功创建网关级 VirtualService: %s/%s", VirtualServiceNamespace, GatewayVirtualServiceName)
	return nil
}

// updateGatewayVS 更新现有的网关级别 VirtualService
func (vsm *VirtualServiceManager) updateGatewayVS(ctx context.Context, gatewayVS *istiov1beta1.VirtualService, serviceInfo *ServiceInfo) error {
	klog.Infof("网关级 VirtualService %s/%s 已存在，检查是否需要更新", VirtualServiceNamespace, GatewayVirtualServiceName)

	updatedGatewayVS := gatewayVS.DeepCopy()

	// 检查是否已存在该服务的路由规则
	routeKey := vsm.getRouteKey(serviceInfo)
	if vsm.routeExistsForService(updatedGatewayVS, routeKey) {
		klog.Infof("服务 %s 的路由规则已存在，无需更新网关级 VirtualService", serviceInfo.Name)
		return nil
	}

	// 添加新的路由规则
	newRoutes := vsm.routeBuilder.BuildGatewayRoutes(serviceInfo)
	updatedGatewayVS.Spec.Http = append(updatedGatewayVS.Spec.Http, newRoutes...)

	klog.Infof("更新网关级 VirtualService，添加服务 %s 的路由规则", serviceInfo.Name)
	if err := vsm.istioClient.UpdateVirtualService(ctx, VirtualServiceNamespace, updatedGatewayVS, metav1.UpdateOptions{}); err != nil {
		klog.Errorf("更新网关级 VirtualService 失败: %v", err)
		return fmt.Errorf("failed to update gateway VirtualService: %v", err)
	}
	klog.Infof("成功更新网关级 VirtualService: %s/%s", VirtualServiceNamespace, GatewayVirtualServiceName)
	return nil
}

// getRouteKey 获取路由匹配的关键字
func (vsm *VirtualServiceManager) getRouteKey(serviceInfo *ServiceInfo) string {
	if serviceInfo.DevType != "" {
		// OnlineDev 服务使用服务名作为前缀
		return serviceInfo.Name
	} else {
		// RayCluster 服务使用 rayClusterName 作为前缀
		return serviceInfo.RayClusterName
	}
}

// routeExistsForService 检查是否已存在指定服务的路由规则
func (vsm *VirtualServiceManager) routeExistsForService(gatewayVS *istiov1beta1.VirtualService, routeKey string) bool {
	expectedPrefix := fmt.Sprintf("/%s/", routeKey)
	for _, route := range gatewayVS.Spec.Http {
		if len(route.Match) > 0 && route.Match[0].Uri != nil &&
			route.Match[0].Uri.GetPrefix() == expectedPrefix {
			return true
		}
	}
	return false
}

// RemoveServiceFromVS 从 VirtualService 中移除服务相关的路由规则
func (vsm *VirtualServiceManager) RemoveServiceFromVS(ctx context.Context, serviceName string, routeKey string, isOnlineDev bool) error {
	klog.Infof("开始删除服务 %s 的路由规则", serviceName)

	// 1. 删除服务级别的 VS（只有 RayCluster 服务才有）
	if !isOnlineDev {
		if err := vsm.removeServiceVS(ctx, serviceName); err != nil {
			return err
		}
	}

	// 2. 从网关级别的 VS 中移除对应路由
	return vsm.removeServiceFromGatewayVS(ctx, routeKey)
}

// removeServiceVS 删除服务级别的 VirtualService
func (vsm *VirtualServiceManager) removeServiceVS(ctx context.Context, serviceName string) error {
	serviceVSName := fmt.Sprintf(ServiceVSNameFormat, serviceName)
	klog.Infof("删除服务级 VirtualService: %s/%s", VirtualServiceNamespace, serviceVSName)

	err := vsm.istioClient.DeleteVirtualService(ctx, VirtualServiceNamespace, serviceVSName, metav1.DeleteOptions{})
	if err != nil {
		if !k8sErrors.IsNotFound(err) {
			klog.Errorf("删除服务级 VirtualService %s/%s 失败: %v", VirtualServiceNamespace, serviceVSName, err)
			return fmt.Errorf("删除服务级别 VirtualService 失败: %v", err)
		}
		klog.Infof("服务级 VirtualService %s/%s 不存在，无需删除", VirtualServiceNamespace, serviceVSName)
	} else {
		klog.Infof("成功删除服务级 VirtualService: %s/%s", VirtualServiceNamespace, serviceVSName)
	}
	return nil
}

// removeServiceFromGatewayVS 从网关级别的 VirtualService 中移除服务路由
func (vsm *VirtualServiceManager) removeServiceFromGatewayVS(ctx context.Context, routeKey string) error {
	klog.Infof("从网关级 VirtualService 中移除路由前缀 %s 的路由规则", routeKey)

	gatewayVS, err := vsm.istioClient.GetVirtualService(ctx, VirtualServiceNamespace, GatewayVirtualServiceName, metav1.GetOptions{})
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			klog.Warningf("网关 VirtualService:%s 不存在，无需处理", GatewayVirtualServiceName)
			return nil
		}
		klog.Errorf("获取网关级 VirtualService %s/%s 失败: %v", VirtualServiceNamespace, GatewayVirtualServiceName, err)
		return fmt.Errorf("获取网关 VirtualService 失败: %v", err)
	}

	updatedGatewayVS := gatewayVS.DeepCopy()
	if updatedGatewayVS.Spec.Http == nil {
		klog.Infof("网关级 VirtualService %s/%s 没有 HTTP 路由规则", VirtualServiceNamespace, GatewayVirtualServiceName)
		return nil
	}

	// 过滤掉要删除的路由
	filteredRoutes, removedCount := vsm.filterRoutesForService(updatedGatewayVS.Spec.Http, routeKey)

	// 如果路由没有变化，不需要更新
	if removedCount == 0 {
		klog.Infof("网关级 VirtualService 中没有找到路由前缀 %s 的路由规则，无需更新", routeKey)
		return nil
	}

	if len(filteredRoutes) == 0 {
		klog.Warningf("网关级 VirtualService 中的所有路由规则都与路由前缀 %s 相关，删除后将没有路由规则，无需更新", routeKey)
		return nil
	}

	klog.Infof("更新网关级 VirtualService，删除了 %d 个与路由前缀 %s 相关的路由规则", removedCount, routeKey)
	updatedGatewayVS.Spec.Http = filteredRoutes
	if err := vsm.istioClient.UpdateVirtualService(ctx, VirtualServiceNamespace, updatedGatewayVS, metav1.UpdateOptions{}); err != nil {
		klog.Errorf("更新网关级 VirtualService 失败: %v", err)
		return fmt.Errorf("更新网关级 VirtualService 失败: %v", err)
	}
	klog.Infof("成功从网关级 VirtualService 中移除路由前缀 %s 的路由规则", routeKey)
	return nil
}

// filterRoutesForService 过滤掉指定路由前缀的路由规则
func (vsm *VirtualServiceManager) filterRoutesForService(routes []*v1beta1.HTTPRoute, routeKey string) ([]*v1beta1.HTTPRoute, int) {
	filteredRoutes := make([]*v1beta1.HTTPRoute, 0)
	removedCount := 0
	expectedPrefix := fmt.Sprintf("/%s/", routeKey)

	for _, route := range routes {
		if len(route.Match) > 0 && route.Match[0].Uri != nil &&
			route.Match[0].Uri.GetPrefix() == expectedPrefix {
			// 跳过要删除的路由
			removedCount++
			continue
		}
		filteredRoutes = append(filteredRoutes, route)
	}

	return filteredRoutes, removedCount
}
